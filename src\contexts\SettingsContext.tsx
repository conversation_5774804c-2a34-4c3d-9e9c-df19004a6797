'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'


// Settings Types
export interface StoreSettings {
  name: string
  address: string
  phone: string
  email: string
  website: string
  currency: string
  timezone: string
  businessHours: {
    open: string
    close: string
  }
  operatingDays: string[]
  businessRegistration: {
    registrationNumber: string
    taxId: string
    businessType: string
    registrationDate: string
  }
  locations: Array<{
    id: number
    name: string
    address: string
    phone: string
    isMain: boolean
  }>
  branding: {
    logo: string | null
    primaryColor: string
    secondaryColor: string
    slogan: string
  }
}

export interface ProfileSettings {
  firstName: string
  lastName: string
  email: string
  phone: string
  role: string
  avatar: string | null
  bio: string
  dateOfBirth: string
  address: string
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
  preferences: {
    language: string
    timezone: string
    dateFormat: string
    numberFormat: string
  }
}

export interface NotificationSettings {
  lowStock: boolean
  newDebt: boolean
  paymentReceived: boolean
  dailyReport: boolean
  weeklyReport: boolean
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  channels: {
    email: string
    sms: string
    webhook: string
  }
  customRules: Array<{
    id: number
    name: string
    condition: string
    action: string
    enabled: boolean
  }>
  templates: {
    lowStock: string
    newDebt: string
    paymentReceived: string
  }
}

export interface SecuritySettings {
  twoFactorAuth: boolean
  sessionTimeout: string
  passwordExpiry: string
  loginAttempts: string
  currentPassword: string
  newPassword: string
  confirmPassword: string
  apiKeys: Array<{
    id: number
    name: string
    key: string
    created: string
    lastUsed: string
    permissions: string[]
  }>
  loginHistory: Array<{
    id: number
    timestamp: string
    ip: string
    device: string
    location: string
    success: boolean
  }>
  passwordPolicy: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSymbols: boolean
  }
}

export interface AppearanceSettings {
  theme: string
  language: string
  dateFormat: string
  numberFormat: string
  colorScheme: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
  }
  layout: {
    sidebarPosition: string
    density: string
    showAnimations: boolean
    compactMode: boolean
  }
  typography: {
    fontFamily: string
    fontSize: string
    fontWeight: string
  }
}

export interface BackupSettings {
  autoBackup: boolean
  backupFrequency: string
  retentionDays: string
  lastBackup: string
  cloudStorage: {
    provider: string
    bucket: string
    accessKey: string
    secretKey: string
  }
  backupHistory: Array<{
    id: number
    timestamp: string
    size: string
    status: string
    type: string
  }>
  verification: {
    enabled: boolean
    lastVerified: string
    status: string
  }
}

export interface AllSettings {
  store: StoreSettings
  profile: ProfileSettings
  notifications: NotificationSettings
  security: SecuritySettings
  appearance: AppearanceSettings
  backup: BackupSettings
}

interface SettingsContextType {
  settings: AllSettings
  updateSettings: (section: keyof AllSettings, newSettings: Partial<AllSettings[keyof AllSettings]>) => void
  saveSettings: () => Promise<void>
  resetSettings: (section?: keyof AllSettings) => void
  refreshSettings: () => Promise<void>
  isLoading: boolean
  hasUnsavedChanges: boolean
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

// Default settings
const defaultSettings: AllSettings = {
  store: {
    name: 'Revantad Store',
    address: '123 Barangay Street, Manila, Philippines',
    phone: '+63 ************',
    email: '<EMAIL>',
    website: 'https://revantadstore.com',
    currency: 'PHP',
    timezone: 'Asia/Manila',
    businessHours: { open: '06:00', close: '22:00' },
    operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    businessRegistration: {
      registrationNumber: 'REG-2024-001',
      taxId: 'TAX-*********',
      businessType: 'Retail',
      registrationDate: '2024-01-01',
    },
    locations: [{
      id: 1,
      name: 'Main Store',
      address: '123 Barangay Street, Manila, Philippines',
      phone: '+63 ************',
      isMain: true,
    }],
    branding: {
      logo: null,
      primaryColor: '#22c55e',
      secondaryColor: '#facc15',
      slogan: 'Your Neighborhood Store',
    },
  },
  profile: {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+63 ************',
    role: 'Store Owner',
    avatar: null,
    bio: 'Experienced store owner managing Revantad Store operations.',
    dateOfBirth: '1990-01-01',
    address: '123 Barangay Street, Manila, Philippines',
    emergencyContact: {
      name: 'Emergency Contact',
      phone: '+63 ************',
      relationship: 'Family',
    },
    preferences: {
      language: 'en',
      timezone: 'Asia/Manila',
      dateFormat: 'MM/DD/YYYY',
      numberFormat: 'en-US',
    },
  },
  notifications: {
    lowStock: true,
    newDebt: true,
    paymentReceived: true,
    dailyReport: false,
    weeklyReport: true,
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    channels: {
      email: '<EMAIL>',
      sms: '+63 ************',
      webhook: '',
    },
    customRules: [{
      id: 1,
      name: 'Critical Stock Alert',
      condition: 'stock < 5',
      action: 'email + sms',
      enabled: true,
    }],
    templates: {
      lowStock: 'Product {{productName}} is running low ({{currentStock}} remaining)',
      newDebt: 'New debt recorded for {{customerName}}: ₱{{amount}}',
      paymentReceived: 'Payment received from {{customerName}}: ₱{{amount}}',
    },
  },
  security: {
    twoFactorAuth: false,
    sessionTimeout: '30',
    passwordExpiry: '90',
    loginAttempts: '5',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    apiKeys: [{
      id: 1,
      name: 'Main API Key',
      key: 'sk_live_***************',
      created: '2024-01-01',
      lastUsed: '2024-01-20',
      permissions: ['read', 'write'],
    }],
    loginHistory: [{
      id: 1,
      timestamp: '2024-01-20T10:30:00Z',
      ip: '***********',
      device: 'Chrome on Windows',
      location: 'Manila, Philippines',
      success: true,
    }],
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSymbols: true,
    },
  },
  appearance: {
    theme: 'light',
    language: 'en',
    dateFormat: 'MM/DD/YYYY',
    numberFormat: 'en-US',
    colorScheme: {
      primary: '#22c55e',
      secondary: '#facc15',
      accent: '#3b82f6',
      background: '#ffffff',
      surface: '#f8fafc',
    },
    layout: {
      sidebarPosition: 'left',
      density: 'comfortable',
      showAnimations: true,
      compactMode: false,
    },
    typography: {
      fontFamily: 'Inter',
      fontSize: 'medium',
      fontWeight: 'normal',
    },
  },
  backup: {
    autoBackup: true,
    backupFrequency: 'daily',
    retentionDays: '30',
    lastBackup: '2024-01-20T10:30:00Z',
    cloudStorage: {
      provider: 'local',
      bucket: '',
      accessKey: '',
      secretKey: '',
    },
    backupHistory: [
      {
        id: 1,
        timestamp: '2024-01-20T10:30:00Z',
        size: '2.5 MB',
        status: 'completed',
        type: 'automatic',
      },
      {
        id: 2,
        timestamp: '2024-01-19T10:30:00Z',
        size: '2.4 MB',
        status: 'completed',
        type: 'automatic',
      }
    ],
    verification: {
      enabled: true,
      lastVerified: '2024-01-20T10:35:00Z',
      status: 'verified',
    },
  },
}

export function SettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AllSettings>(defaultSettings)
  const [isLoading, setIsLoading] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Load settings from database on mount
  useEffect(() => {
    loadSettingsFromDatabase()
  }, [])

  const loadSettingsFromDatabase = async () => {
    setIsLoading(true)
    try {
      // Try to load from database first
      const response = await fetch('/api/settings')
      if (response.ok) {
        const data = await response.json()
        console.log('🔍 Raw API response:', data)
        const dbSettings = data.settings || {}

        // Merge database settings with defaults
        const mergedSettings: AllSettings = {
          store: dbSettings.store?.store_info?.value || dbSettings.store_info?.value || defaultSettings.store,
          profile: dbSettings.profile?.profile_settings?.value || dbSettings.profile_settings?.value || defaultSettings.profile,
          notifications: dbSettings.notifications?.notification_settings?.value || dbSettings.notification_settings?.value || defaultSettings.notifications,
          security: dbSettings.security?.security_settings?.value || dbSettings.security_settings?.value || defaultSettings.security,
          appearance: dbSettings.appearance?.appearance_settings?.value || dbSettings.appearance_settings?.value || defaultSettings.appearance,
          backup: dbSettings.backup?.backup_settings?.value || dbSettings.backup_settings?.value || defaultSettings.backup,
        }

        setSettings(mergedSettings)
        console.log('✅ Settings loaded from database:', mergedSettings)
        console.log('🔍 Raw database settings:', dbSettings)
        console.log('👤 Profile settings loaded:', mergedSettings.profile)

        // Specific debugging for profile settings
        if (dbSettings.profile && dbSettings.profile.profile_settings) {
          console.log('🎯 Found profile.profile_settings:', dbSettings.profile.profile_settings.value)
        } else if (dbSettings.profile_settings) {
          console.log('🎯 Found direct profile_settings:', dbSettings.profile_settings.value)
        } else {
          console.log('❌ No profile settings found in database response')
          console.log('🔍 Available keys:', Object.keys(dbSettings))
        }
      } else {
        console.warn('⚠️ Settings table not ready yet, using localStorage + defaults')
        throw new Error('Settings table not available')
      }
    } catch (error) {
      console.log('📦 Using localStorage fallback for settings')
      // Fallback to localStorage if database fails
      const savedSettings = localStorage.getItem('revantad-settings')
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings)
          setSettings({ ...defaultSettings, ...parsed })
          console.log('📦 Loaded settings from localStorage')
        } catch (parseError) {
          console.error('Error parsing localStorage settings:', parseError)
          setSettings(defaultSettings)
        }
      } else {
        setSettings(defaultSettings)
        console.log('📦 Using default settings')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const updateSettings = (section: keyof AllSettings, newSettings: Partial<AllSettings[keyof AllSettings]>) => {
    setSettings(prev => ({
      ...prev,
      [section]: { ...prev[section], ...newSettings }
    }))
    setHasUnsavedChanges(true)
  }

  const saveSettings = async () => {
    setIsLoading(true)
    try {
      // Prepare settings for database storage
      const settingsToSave = [
        {
          setting_key: 'store_info',
          setting_value: settings.store,
          setting_category: 'store',
          description: 'Store information and business details',
          is_public: true
        },
        {
          setting_key: 'profile_settings',
          setting_value: settings.profile,
          setting_category: 'profile',
          description: 'User profile and personal settings',
          is_public: false
        },
        {
          setting_key: 'appearance_settings',
          setting_value: settings.appearance,
          setting_category: 'appearance',
          description: 'UI appearance and theme settings',
          is_public: true
        },
        {
          setting_key: 'notification_settings',
          setting_value: settings.notifications,
          setting_category: 'notifications',
          description: 'Notification preferences and templates',
          is_public: false
        },
        {
          setting_key: 'security_settings',
          setting_value: settings.security,
          setting_category: 'security',
          description: 'Security and authentication settings',
          is_public: false
        },
        {
          setting_key: 'backup_settings',
          setting_value: settings.backup,
          setting_category: 'backup',
          description: 'Backup and data retention settings',
          is_public: false
        },
        {
          setting_key: 'profile_settings',
          setting_value: settings.profile,
          setting_category: 'profile',
          description: 'User profile and personal settings',
          is_public: false
        }
      ]

      // Save to database
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: settingsToSave })
      })

      if (!response.ok) {
        throw new Error(`Failed to save settings: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ Settings saved to database:', result)

      // Also save to localStorage as backup
      localStorage.setItem('revantad-settings', JSON.stringify(settings))

      setHasUnsavedChanges(false)

      // Apply theme changes immediately
      if (settings.appearance.theme === 'dark') {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }

    } catch (error) {
      console.error('❌ Error saving settings:', error)
      // Fallback to localStorage only
      localStorage.setItem('revantad-settings', JSON.stringify(settings))
      console.log('📦 Settings saved to localStorage as fallback')
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const resetSettings = (section?: keyof AllSettings) => {
    if (section) {
      setSettings(prev => ({
        ...prev,
        [section]: defaultSettings[section]
      }))
    } else {
      setSettings(defaultSettings)
    }
    setHasUnsavedChanges(true)
  }

  const refreshSettings = async () => {
    await loadSettingsFromDatabase()
  }

  return (
    <SettingsContext.Provider value={{
      settings,
      updateSettings,
      saveSettings,
      resetSettings,
      refreshSettings,
      isLoading,
      hasUnsavedChanges
    }}>
      {children}
    </SettingsContext.Provider>
  )
}

export function useSettings() {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}
