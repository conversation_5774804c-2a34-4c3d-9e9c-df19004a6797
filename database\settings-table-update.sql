-- =====================================================
-- SETTINGS TABLE UPDATE FOR TINDAHAN STORE
-- =====================================================
-- This SQL adds the settings table to your existing database
-- Run this in your Supabase SQL Editor: https://supabase.com/dashboard/project/[your-project]/sql

-- Create settings table
CREATE TABLE IF NOT EXISTS settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value JSONB NOT NULL,
    setting_category VARCHAR(100) NOT NULL DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Data integrity constraints
    CONSTRAINT settings_key_not_empty CHECK (LENGTH(TRIM(setting_key)) > 0),
    CONSTRAINT settings_category_not_empty CHECK (LENGTH(TRIM(setting_category)) > 0),
    CONSTRAINT settings_valid_category CHECK (setting_category IN ('store', 'profile', 'notifications', 'security', 'appearance', 'backup', 'general'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(setting_category);
CREATE INDEX IF NOT EXISTS idx_settings_public ON settings(is_public);
CREATE INDEX IF NOT EXISTS idx_settings_category_key ON settings(setting_category, setting_key);
CREATE INDEX IF NOT EXISTS idx_settings_created_at ON settings(created_at);

-- Add timestamp trigger
CREATE TRIGGER IF NOT EXISTS update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS and create policy
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Enable all operations for application" ON settings;
CREATE POLICY "Enable all operations for application" ON settings FOR ALL USING (true);

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, setting_category, description, is_public)
VALUES 
(
    'store_info',
    '{
        "name": "Revantad Store",
        "address": "123 Barangay Street, Manila, Philippines",
        "phone": "+63 ************",
        "email": "<EMAIL>",
        "website": "https://revantadstore.com",
        "currency": "PHP",
        "timezone": "Asia/Manila",
        "businessHours": {"open": "06:00", "close": "22:00"},
        "operatingDays": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"],
        "businessRegistration": {
            "registrationNumber": "REG-2024-001",
            "taxId": "TAX-*********",
            "businessType": "Retail",
            "registrationDate": "2024-01-01"
        },
        "locations": [{
            "id": 1,
            "name": "Main Store",
            "address": "123 Barangay Street, Manila, Philippines",
            "phone": "+63 ************",
            "isMain": true
        }],
        "branding": {
            "logo": null,
            "primaryColor": "#22c55e",
            "secondaryColor": "#16a34a",
            "slogan": "Your Neighborhood Store"
        }
    }',
    'store',
    'Store information and business details',
    true
),
(
    'appearance_settings',
    '{
        "theme": "light",
        "language": "en",
        "dateFormat": "MM/dd/yyyy",
        "numberFormat": "en-US",
        "colorScheme": {
            "primary": "#22c55e",
            "secondary": "#16a34a",
            "accent": "#10b981",
            "background": "#ffffff",
            "surface": "#f8fafc"
        },
        "layout": {
            "sidebarPosition": "left",
            "density": "comfortable",
            "showAnimations": true,
            "compactMode": false
        },
        "typography": {
            "fontFamily": "Inter",
            "fontSize": "medium",
            "fontWeight": "normal"
        }
    }',
    'appearance',
    'UI appearance and theme settings',
    true
),
(
    'notification_settings',
    '{
        "lowStock": true,
        "newDebt": true,
        "paymentReceived": true,
        "dailyReport": false,
        "weeklyReport": true,
        "emailNotifications": true,
        "smsNotifications": false,
        "pushNotifications": true,
        "channels": {
            "email": "<EMAIL>",
            "sms": "",
            "webhook": ""
        },
        "templates": {
            "lowStock": "Product {{product_name}} is running low ({{quantity}} remaining)",
            "newDebt": "New debt recorded for {{customer_name}}: PHP {{amount}}",
            "paymentReceived": "Payment received from {{customer_name}}: PHP {{amount}}"
        }
    }',
    'notifications',
    'Notification preferences and templates',
    false
),
(
    'security_settings',
    '{
        "twoFactorAuth": false,
        "sessionTimeout": "24",
        "passwordExpiry": "90",
        "loginAttempts": "5"
    }',
    'security',
    'Security and authentication settings',
    false
),
(
    'backup_settings',
    '{
        "autoBackup": true,
        "backupFrequency": "daily",
        "retentionDays": "30",
        "lastBackup": "",
        "cloudStorage": {
            "provider": "",
            "bucket": "",
            "accessKey": "",
            "secretKey": ""
        },
        "verification": {
            "enabled": true,
            "lastVerified": "",
            "status": "pending"
        }
    }',
    'backup',
    'Backup and data retention settings',
    false
)
ON CONFLICT (setting_key) DO NOTHING;

-- Verify the setup
SELECT 
    'Settings Table Setup Complete!' as status,
    COUNT(*) as total_settings,
    COUNT(CASE WHEN setting_category = 'store' THEN 1 END) as store_settings,
    COUNT(CASE WHEN setting_category = 'appearance' THEN 1 END) as appearance_settings,
    COUNT(CASE WHEN setting_category = 'notifications' THEN 1 END) as notification_settings
FROM settings;

-- Show all settings
SELECT setting_key, setting_category, description, is_public, created_at 
FROM settings 
ORDER BY setting_category, setting_key;
